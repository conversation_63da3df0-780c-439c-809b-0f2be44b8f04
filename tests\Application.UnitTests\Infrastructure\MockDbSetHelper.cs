using Microsoft.EntityFrameworkCore;
using Moq;
using System.Linq.Expressions;

namespace PayPing.Settlement.Application.UnitTests.Infrastructure;

/// <summary>
/// Helper class for creating mock DbSets with proper setup for Entity Framework operations
/// </summary>
public static class MockDbSetHelper
{
    /// <summary>
    /// Creates a mock DbSet with the provided data and sets up common Entity Framework operations
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    /// <param name="data">The data to populate the DbSet with</param>
    /// <returns>A configured mock DbSet</returns>
    public static Mock<DbSet<T>> CreateMockDbSet<T>(IEnumerable<T> data) where T : class
    {
        var queryableData = data.AsQueryable();
        var mockDbSet = new Mock<DbSet<T>>();

        // Setup IQueryable operations
        mockDbSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(queryableData.Provider);
        mockDbSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryableData.Expression);
        mockDbSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryableData.ElementType);
        mockDbSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(queryableData.GetEnumerator());

        // Setup async operations
        mockDbSet.As<IAsyncEnumerable<T>>()
            .Setup(m => m.GetAsyncEnumerator(It.IsAny<CancellationToken>()))
            .Returns(new TestAsyncEnumerator<T>(queryableData.GetEnumerator()));

        mockDbSet.As<IQueryable<T>>()
            .Setup(m => m.Provider)
            .Returns(new TestAsyncQueryProvider<T>(queryableData.Provider));

        // Setup Add operations
        mockDbSet.Setup(m => m.Add(It.IsAny<T>())).Callback<T>(entity =>
        {
            // In a real scenario, you might want to add to a backing collection
            // For unit tests, we typically just verify the Add was called
        });

        mockDbSet.Setup(m => m.AddRange(It.IsAny<IEnumerable<T>>())).Callback<IEnumerable<T>>(entities =>
        {
            // In a real scenario, you might want to add to a backing collection
            // For unit tests, we typically just verify the AddRange was called
        });

        // Setup Remove operations
        mockDbSet.Setup(m => m.Remove(It.IsAny<T>())).Callback<T>(entity =>
        {
            // In a real scenario, you might want to remove from a backing collection
            // For unit tests, we typically just verify the Remove was called
        });

        mockDbSet.Setup(m => m.RemoveRange(It.IsAny<IEnumerable<T>>())).Callback<IEnumerable<T>>(entities =>
        {
            // In a real scenario, you might want to remove from a backing collection
            // For unit tests, we typically just verify the RemoveRange was called
        });

        return mockDbSet;
    }

    /// <summary>
    /// Creates a mock DbSet with empty data
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    /// <returns>A configured mock DbSet with no data</returns>
    public static Mock<DbSet<T>> CreateEmptyMockDbSet<T>() where T : class
    {
        return CreateMockDbSet<T>(new List<T>());
    }
}

/// <summary>
/// Test implementation of IAsyncEnumerator for mocking async operations
/// </summary>
/// <typeparam name="T">The entity type</typeparam>
internal class TestAsyncEnumerator<T> : IAsyncEnumerator<T>
{
    private readonly IEnumerator<T> _inner;

    public TestAsyncEnumerator(IEnumerator<T> inner)
    {
        _inner = inner;
    }

    public T Current => _inner.Current;

    public ValueTask<bool> MoveNextAsync()
    {
        return ValueTask.FromResult(_inner.MoveNext());
    }

    public ValueTask DisposeAsync()
    {
        _inner.Dispose();
        return ValueTask.CompletedTask;
    }
}

/// <summary>
/// Test implementation of IAsyncQueryProvider for mocking async LINQ operations
/// </summary>
/// <typeparam name="TEntity">The entity type</typeparam>
internal class TestAsyncQueryProvider<TEntity> : IAsyncQueryProvider
{
    private readonly IQueryProvider _inner;

    internal TestAsyncQueryProvider(IQueryProvider inner)
    {
        _inner = inner;
    }

    public IQueryable CreateQuery(Expression expression)
    {
        return new TestAsyncEnumerable<TEntity>(expression);
    }

    public IQueryable<TElement> CreateQuery<TElement>(Expression expression)
    {
        return new TestAsyncEnumerable<TElement>(expression);
    }

    public object Execute(Expression expression)
    {
        return _inner.Execute(expression);
    }

    public TResult Execute<TResult>(Expression expression)
    {
        return _inner.Execute<TResult>(expression);
    }

    public TResult ExecuteAsync<TResult>(Expression expression, CancellationToken cancellationToken = default)
    {
        var resultType = typeof(TResult).GetGenericArguments()[0];
        var executionResult = ((IQueryProvider)this).Execute(expression);

        return (TResult)typeof(Task).GetMethod(nameof(Task.FromResult))
            ?.MakeGenericMethod(resultType)
            ?.Invoke(null, new[] { executionResult });
    }
}

/// <summary>
/// Test implementation of IAsyncEnumerable for mocking async LINQ operations
/// </summary>
/// <typeparam name="T">The entity type</typeparam>
internal class TestAsyncEnumerable<T> : EnumerableQuery<T>, IAsyncEnumerable<T>, IQueryable<T>
{
    public TestAsyncEnumerable(IEnumerable<T> enumerable) : base(enumerable) { }

    public TestAsyncEnumerable(Expression expression) : base(expression) { }

    public IAsyncEnumerator<T> GetAsyncEnumerator(CancellationToken cancellationToken = default)
    {
        return new TestAsyncEnumerator<T>(this.AsEnumerable().GetEnumerator());
    }

    IQueryProvider IQueryable.Provider => new TestAsyncQueryProvider<T>(this);
}
