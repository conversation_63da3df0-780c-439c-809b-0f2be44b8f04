﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Helpers;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders.Commands;

public sealed record SubmitOrderResponse(string Message);

public sealed class SubmitOrderController : ApiControllerBase
{
    /// <summary>
    /// Submits an order with the given order ID.
    /// </summary>
    /// <param name="orderId">The unique identifier of the order to be submitted.</param>
    /// <param name="twoStepCode">2FA code</param>
    /// <param name="twoStepType">Verify Type</param>
    /// <returns>
    /// Returns an HTTP 200 OK response with a <see cref="CreateOrderResponse"/> when the operation is successful.
    /// Returns a <see cref="ProblemDetails"/> with status code 400 if the submission fails.
    /// </returns>
    [HttpPost("{orderId:guid}/submit-order")]
    [Authorize("write")]
    [ProducesResponseType<CreateOrderResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SubmitOrder(Guid orderId, string twoStepCode, TwoStepType twoStepType = TwoStepType.SMS)
    {
        var command = new SubmitOrderCommand(orderId, twoStepCode, twoStepType);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record SubmitOrderCommand(
    Guid OrderId,
    string TwoStepCode,
    TwoStepType TwoStepType = TwoStepType.SMS)
    : IRequest<SubmitOrderCommand, Task<ErrorOr<SubmitOrderResponse>>>;

public sealed class SubmitOrderCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IEventServiceWrapper eventService,
    IWalletService walletService)
    : IRequestHandler<SubmitOrderCommand, Task<ErrorOr<SubmitOrderResponse>>>
{
    public async Task<ErrorOr<SubmitOrderResponse>> Handle(SubmitOrderCommand request,
        CancellationToken cancellationToken)
    {
        if (currentUserService.UserId == null)
            return Error.Unauthorized("شما مجوز انجام این عملیات را ندارید.");

        var userId = currentUserService.UserId.Value;
        var twoStepValidation = await eventService.ValidateTwoStepVerificationAsync(
            userId,
            request.OrderId,
            request.TwoStepCode,
            request.TwoStepType,
            cancellationToken);

        if (twoStepValidation.IsError)
            return twoStepValidation.Errors;

        if (await IsBannedUser(userId, cancellationToken))
        {
            return Error.Validation(
                code: "UserBanned",
                description: "متاسفانه شما دسترسی ثبت درخواست تسویه ندارید. با پشتیبانی در ارتباط باشید");
        }

        var order = await dbContext.Orders
            .AsTracking()
            .Include(o => o.OrderDetails)
            .Where(o => o.Id == request.OrderId)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is not { Status: OrderStatus.Draft } || order.OrderDetails.Count == 0)
            return Error.NotFound(description: "کد درخواست نامعتبر می‌باشد");

        var settlementWalletId = await dbContext.UserWalletInformations
            .Where(x => x.UserId == userId)
            .Select(x => x.SettlementWalletId)
            .FirstOrDefaultAsync(cancellationToken);

        if (settlementWalletId == null)
            return Error.NotFound(description: "کیف پول تسویه یافت نشد.");

        order.WalletProcessing();
        await dbContext.SaveChangesAsync(cancellationToken);

        var totalOrderAmount = decimal.Add(
            order.OrderDetails.Sum(x => x.Amount),
            order.OrderDetails.Sum(x => x.WageAmount));

        var blockTransaction = await walletService.BlockOrderAmount(
            settlementWalletId,
            order.WalletBlockCorrelationId,
            totalOrderAmount,
            cancellationToken);

        if (blockTransaction.IsError)
        {
            order.Fail();
            await dbContext.SaveChangesAsync(cancellationToken);
            return Error.Forbidden(description: "خطای در کیف پول تسویه");
        }

        order.Submit();

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0
            ? new SubmitOrderResponse("درخواست شما با موفقیت ثبت شد و درحال پردازش است.")
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }

    private Task<bool> IsBannedUser(int userId, CancellationToken cancellationToken)
    {
        return dbContext.UserConfigs
            .AsNoTracking()
            .Where(u => u.Id == userId)
            .Select(uc => uc.IsBanned)
            .FirstOrDefaultAsync(cancellationToken);
    }
}

public sealed class SubmitOrderCommandValidator : AbstractValidator<SubmitOrderCommand>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;
    private readonly IDateTime _dateTime;

    public SubmitOrderCommandValidator(
        ApplicationDbContext dbContext,
        ICurrentUserService currentUserService,
        IDateTime dateTime)
    {
        _dbContext = dbContext;
        _currentUserService = currentUserService;
        _dateTime = dateTime;

        WhenAsync(ReachedMaximumOrderDetailsCount,
            () => throw new ReachedMaximumOrderDetailsCountException(UserOptions.GetMaxSettlementCountPerRequest));

        RuleFor(x => x.OrderId)
            .MustAsync(ValidateDailyTransferLimit)
            .WithMessage("{DailyLimitMessage}");

        WhenAsync(UserIsCritical,
            () =>
            {
                RuleFor(x => x.OrderId)
                    .MustAsync(ValidateTransferLimits)
                    .WithMessage("{PropertyValue}");
            });
    }

    private async Task<bool> ReachedMaximumOrderDetailsCount(SubmitOrderCommand request, CancellationToken ct)
    {
        var detailsCount = await _dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.OrderId == request.OrderId)
            .CountAsync(ct);

        return detailsCount >= UserOptions.GetMaxSettlementCountPerRequest;
    }

    private async Task<bool> UserIsCritical(SubmitOrderCommand _, CancellationToken ct)
    {
        return await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == _currentUserService.UserId)
            .Select(x => x.IsCritical)
            .FirstOrDefaultAsync(ct);
    }

    private async Task<bool> ValidateTransferLimits(
        SubmitOrderCommand request,
        Guid orderId,
        ValidationContext<SubmitOrderCommand> context,
        CancellationToken ct)
    {
        var failedIbans = await GetFailedIbans(orderId, ct);

        if (failedIbans.Count == 0) return true;

        var ibanList = string.Join("، ", failedIbans);

        context.MessageFormatter.AppendArgument("PropertyValue",
            $"امکان واریز بیش از 100میلیون تومان در ۲۴ساعت برای شبا‌های {ibanList} نمی‌باشد");

        return false;
    }

    private async Task<List<string>> GetFailedIbans(Guid orderId, CancellationToken ct)
    {
        var today = _dateTime.Now.Date;
        var tomorrow = today.AddDays(1);
        var allowedStatuses = new[] { OrderDetailStatus.InProgress, OrderDetailStatus.Success };

        var transferSummary = await _dbContext.OrderDetails
            .GetTodayInProgressOrderDetailsWithCurrentOrderDetails
                (_currentUserService.UserId!.Value, orderId, today, tomorrow, allowedStatuses)
            .GroupBy(x => x.Iban)
            .Select(g => new
            {
                Iban = g.Key,
                TotalAmount = g.Sum(y => y.Amount),
                HasCurrentOrder = g.Any(y => y.OrderId == orderId)
            })
            .Where(x => x.HasCurrentOrder)
            .ToListAsync(ct);

        return transferSummary
            .Where(x => x.TotalAmount > UserOptions.GetMaxLast24Amount)
            .Select(x => x.Iban.ToNormalizedString())
            .ToList();
    }

    private async Task<bool> ValidateDailyTransferLimit(
        SubmitOrderCommand request,
        Guid orderId,
        ValidationContext<SubmitOrderCommand> context,
        CancellationToken ct)
    {
        var today = _dateTime.Now.Date;
        var tomorrow = today.AddDays(1);
        var allowedStatuses = new[] { OrderDetailStatus.InProgress, OrderDetailStatus.Success };
        var userId = _currentUserService.UserId!.Value;

        var dailyTransferLimit = await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == userId)
            .Select(x => x.DailyTransferLimit)
            .FirstOrDefaultAsync(ct);

        var transferData = await _dbContext.OrderDetails
            .GetTodayInProgressOrderDetailsWithCurrentOrderDetails(
                userId, orderId, today, tomorrow, allowedStatuses)
            .GroupBy(x => x.OrderId)
            .Select(x => new
            {
                OrderId = x.Key,
                Amount = x.Sum(s => s.Amount),
                IsCurrentOrder = x.Key == orderId
            }).ToListAsync(ct);

        var currentOrderAmount = transferData
            .Where(x => x.IsCurrentOrder)
            .Sum(x => x.Amount);

        var existingTodayAmount = transferData
            .Where(x => !x.IsCurrentOrder)
            .Sum(x => x.Amount);

        var totalAmount = currentOrderAmount + existingTodayAmount;

        if (totalAmount <= dailyTransferLimit) return true;

        context.MessageFormatter.AppendArgument("DailyLimitMessage",
            string.Format(
                ErrorMessages.MessageFormats.DailyTransferLimitExceeded,
                currentOrderAmount.ToCommas(),
                dailyTransferLimit.ToCommas(),
                existingTodayAmount.ToCommas()
            ));

        return false;
    }
}

public static class Helpers
{
    public static IQueryable<OrderDetail> GetTodayInProgressOrderDetailsWithCurrentOrderDetails(
        this DbSet<OrderDetail> orderDetails,
        int userId,
        Guid orderId,
        DateTime today,
        DateTime tomorrow,
        OrderDetailStatus[] allowedStatuses)
    {
        return orderDetails
            .AsNoTracking()
            .Where(x => x.CreatedBy == userId)
            .Where(x => x.Created >= today && x.Created < tomorrow)
            .Where(x => allowedStatuses.Contains(x.Status) || x.OrderId == orderId);
    }
}