using FluentValidation.TestHelper;
using Zify.Settlement.Application.Features.UserConfigurations;

namespace PayPing.Settlement.Application.UnitTests.Features.UserConfigurations.UpdateUserIban;

public class UpdateUserIbanCommandValidatorTests
{
    private readonly UpdateUserIbanCommandValidator _validator;

    public UpdateUserIbanCommandValidatorTests()
    {
        _validator = new UpdateUserIbanCommandValidator();
    }

    [Fact]
    public void Validate_ShouldPass_WhenValidIranianIbanProvided()
    {
        // Arrange
        var command = new UpdateUserIbanCommand("**************************");

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    public void Validate_ShouldPass_WhenValidIranianIbansProvided(string validIban)
    {
        // Arrange
        var command = new UpdateUserIbanCommand(validIban);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("**************************")] // UK IBAN
    [InlineData("**************************")] // German IBAN
    [InlineData("**************************")] // French IBAN
    [InlineData("**************************")] // US format
    [InlineData("123456789012345678901234")]   // No country code
    public void Validate_ShouldFail_WhenIbanDoesNotStartWithIR(string invalidIban)
    {
        // Arrange
        var command = new UpdateUserIbanCommand(invalidIban);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Iban)
            .WithErrorMessage("IBAN must start with 'IR'");
    }

    [Theory]
    [InlineData("**************************")] // Contains letter at the end
    [InlineData("**************************")] // Contains letters at the end
    [InlineData("IRA23456789012345678901234")] // Contains letter after IR
    [InlineData("**************************")] // Contains letter in the middle
    [InlineData("***********************-34")] // Contains dash
    [InlineData("*********************** 34")] // Contains space
    public void Validate_ShouldFail_WhenIbanContainsNonDigitsAfterIR(string invalidIban)
    {
        // Arrange
        var command = new UpdateUserIbanCommand(invalidIban);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Iban)
            .WithErrorMessage("IBAN must contain only digits after 'IR'");
    }

    [Theory]
    [InlineData("   ")] // Whitespace only
    public void Validate_ShouldFail_WhenIbanIsNullOrEmpty(string? invalidIban)
    {
        // Arrange
        var command = new UpdateUserIbanCommand(invalidIban!);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Iban);
    }

    [Theory]
    [InlineData("IR12345")] // Too short
    [InlineData("*********************")] // Too short
    [InlineData("**************************56789")] // Too long
    public void Validate_ShouldStillValidateFormat_EvenWithIncorrectLength(string iban)
    {
        // Arrange
        var command = new UpdateUserIbanCommand(iban);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        // The validator only checks format (starts with IR and contains only digits after IR)
        // Length validation is handled at the request level and by the Iban value object
        if (iban.StartsWith("IR") && iban[2..].All(char.IsDigit))
        {
            result.ShouldNotHaveValidationErrorFor(x => x.Iban);
        }
        else
        {
            result.ShouldHaveValidationErrorFor(x => x.Iban);
        }
    }

    [Fact]
    public void Validate_ShouldFail_WhenMultipleValidationRulesAreBroken()
    {
        // Arrange
        var command = new UpdateUserIbanCommand("**************************");

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Iban)
            .WithErrorMessage("IBAN must start with 'IR'");

        // Note: FluentValidation stops at the first failed rule, so we won't see the second error
        // unless the first one passes
    }

    [Theory]
    [InlineData("ir123456789012345678901234")] // Lowercase
    [InlineData("Ir123456789012345678901234")] // Mixed case
    [InlineData("iR123456789012345678901234")] // Mixed case
    public void Validate_ShouldFail_WhenIbanIsNotUppercaseIR(string invalidIban)
    {
        // Arrange
        var command = new UpdateUserIbanCommand(invalidIban);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Iban)
            .WithErrorMessage("IBAN must start with 'IR'");
    }

    [Fact]
    public void Validate_ShouldPass_WhenIbanHasExactly24DigitsAfterIR()
    {
        // Arrange - IR + 24 digits = 26 characters total
        var command = new UpdateUserIbanCommand("**************************");

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("**************************")] // All zeros
    [InlineData("**************************")] // All nines
    [InlineData("**************************")] // Mixed digits
    [InlineData("**************************")] // Reverse order
    public void Validate_ShouldPass_ForVariousValidDigitCombinations(string validIban)
    {
        // Arrange
        var command = new UpdateUserIbanCommand(validIban);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }
}
