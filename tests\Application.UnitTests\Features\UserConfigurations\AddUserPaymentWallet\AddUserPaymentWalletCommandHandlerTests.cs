using Microsoft.EntityFrameworkCore;
using Moq;
using PayPing.Settlement.Application.UnitTests.Infrastructure;
using Quartz;
using Shouldly;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Features.UserConfigurations;
using Zify.Settlement.Application.Features.Wallets.Jobs;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace PayPing.Settlement.Application.UnitTests.Features.UserConfigurations.AddUserPaymentWallet;

public class AddUserPaymentWalletCommandHandlerTests
{
    private readonly AddUserPaymentWalletCommandHandler _handler;
    private readonly Mock<ISchedulerFactory> _schedulerFactoryMock;
    private readonly Mock<IScheduler> _schedulerMock;
    private readonly Mock<IApplicationDbContext> _mockDbContext;
    private readonly Mock<DbSet<UserConfig>> _mockUserConfigsDbSet;
    private readonly Mock<DbSet<UserWalletInformation>> _mockUserWalletInformationsDbSet;

    public AddUserPaymentWalletCommandHandlerTests()
    {
        _schedulerMock = new Mock<IScheduler>();
        _schedulerFactoryMock = new Mock<ISchedulerFactory>();
        _schedulerFactoryMock.Setup(x => x.GetScheduler(It.IsAny<CancellationToken>()))
            .ReturnsAsync(_schedulerMock.Object);

        _mockDbContext = new Mock<IApplicationDbContext>();
        _mockUserConfigsDbSet = MockDbSetHelper.CreateEmptyMockDbSet<UserConfig>();
        _mockUserWalletInformationsDbSet = MockDbSetHelper.CreateEmptyMockDbSet<UserWalletInformation>();

        _mockDbContext.Setup(x => x.UserConfigs).Returns(_mockUserConfigsDbSet.Object);
        _mockDbContext.Setup(x => x.UserWalletInformations).Returns(_mockUserWalletInformationsDbSet.Object);
        _mockDbContext.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>())).ReturnsAsync(1);

        _handler = new AddUserPaymentWalletCommandHandler(_mockDbContext.Object, _schedulerFactoryMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldCreateNewUserConfigWithWalletInformation_WhenUserConfigDoesNotExist()
    {
        // Arrange
        var userId = 123;
        var paymentWalletId = Guid.NewGuid().ToString();
        var iban = Iban.Of("**************************");
        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId, iban);

        // Setup empty user configs (no existing user config)
        _mockUserConfigsDbSet.Setup(x => x.AsTracking()).Returns(_mockUserConfigsDbSet.Object);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        _schedulerFactoryMock.Verify(x => x.GetScheduler(It.IsAny<CancellationToken>()), Times.Once);
        _schedulerMock.Verify(x => x.TriggerJob(It.Is<JobKey>(j => j.Name == nameof(CreateSettlementWalletJob)), It.IsAny<CancellationToken>()), Times.Once);

        // Verify that a new UserConfig was added
        _mockUserConfigsDbSet.Verify(x => x.Add(It.Is<UserConfig>(uc =>
            uc.UserId == userId &&
            uc.WalletInformation != null &&
            uc.WalletInformation.UserId == userId &&
            uc.WalletInformation.PaymentWalletId.Value == Guid.Parse(paymentWalletId))), Times.Once);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldUpdateExistingWalletInformation_WhenUserConfigExists()
    {
        // Arrange
        var userId = 456;
        var originalWalletId = Guid.NewGuid();
        var newWalletId = Guid.NewGuid();
        var iban = Iban.Of("**************************");

        // Create an existing user config with wallet information
        var existingWalletInfo = UserWalletInformation.Create(userId, originalWalletId);
        var existingUserConfig = UserConfig.Create(existingWalletInfo, Iban.Of("**************************"));

        // Setup mock to return existing user config
        var existingUserConfigs = new List<UserConfig> { existingUserConfig };
        var mockExistingDbSet = MockDbSetHelper.CreateMockDbSet(existingUserConfigs);
        mockExistingDbSet.Setup(x => x.AsTracking()).Returns(mockExistingDbSet.Object);
        _mockDbContext.Setup(x => x.UserConfigs).Returns(mockExistingDbSet.Object);

        var command = new AddUserPaymentWalletCommand(userId, newWalletId.ToString(), iban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        _schedulerFactoryMock.Verify(x => x.GetScheduler(It.IsAny<CancellationToken>()), Times.Once);
        _schedulerMock.Verify(x => x.TriggerJob(It.Is<JobKey>(j => j.Name == nameof(CreateSettlementWalletJob)), It.IsAny<CancellationToken>()), Times.Once);

        // Verify that the existing wallet information was updated
        existingUserConfig.WalletInformation.PaymentWalletId.Value.ShouldBe(newWalletId);
        existingUserConfig.WalletInformation.PaymentWalletId.Value.ShouldNotBe(originalWalletId);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenInvalidWalletIdProvided()
    {
        // Arrange
        var userId = 789;
        var invalidWalletId = "invalid-guid";
        var iban = Iban.Of("**************************");

        var command = new AddUserPaymentWalletCommand(userId, invalidWalletId, iban);

        // Setup empty user configs
        _mockUserConfigsDbSet.Setup(x => x.AsTracking()).Returns(_mockUserConfigsDbSet.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidWalletIdException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenEmptyGuidProvided()
    {
        // Arrange
        var userId = 101;
        var emptyGuid = Guid.Empty.ToString();
        var iban = Iban.Of("**************************");

        var command = new AddUserPaymentWalletCommand(userId, emptyGuid, iban);

        // Setup empty user configs
        _mockUserConfigsDbSet.Setup(x => x.AsTracking()).Returns(_mockUserConfigsDbSet.Object);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidWalletIdException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ShouldCreateUserConfigWithDefaultValues_WhenCreatingNew()
    {
        // Arrange
        var userId = 202;
        var paymentWalletId = Guid.NewGuid().ToString();
        var iban = Iban.Of("**************************");

        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId, iban);

        // Setup empty user configs
        _mockUserConfigsDbSet.Setup(x => x.AsTracking()).Returns(_mockUserConfigsDbSet.Object);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        _schedulerFactoryMock.Verify(x => x.GetScheduler(It.IsAny<CancellationToken>()), Times.Once);
        _schedulerMock.Verify(x => x.TriggerJob(It.Is<JobKey>(j => j.Name == nameof(CreateSettlementWalletJob)), It.IsAny<CancellationToken>()), Times.Once);

        // Verify that a new UserConfig was added with default values
        _mockUserConfigsDbSet.Verify(x => x.Add(It.Is<UserConfig>(uc =>
            uc.UserId == userId &&
            uc.AcceptorCode == string.Empty &&
            uc.IsCritical == false &&
            uc.IsFree == false &&
            uc.IsDepositActivate == false &&
            uc.IsBanned == false &&
            uc.WageType == WageType.Fixed &&
            uc.WageValue == 0 &&
            uc.Max == 0 &&
            uc.Min == 0 &&
            uc.MaxSettlementAmount == 0 &&
            uc.PlanType == SettlementPlanType.Basic &&
            uc.AllowSettlementRegistration == false &&
            uc.AuthenticatorTotpEnabled == false &&
            uc.AuthenticatorTotpSecretKey == string.Empty)), Times.Once);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldHandleCancellationToken_WhenCancellationRequested()
    {
        // Arrange
        var userId = 303;
        var paymentWalletId = Guid.NewGuid().ToString();
        var iban = Iban.Of("**************************");

        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId, iban);

        // Setup empty user configs
        _mockUserConfigsDbSet.Setup(x => x.AsTracking()).Returns(_mockUserConfigsDbSet.Object);

        var cancellationTokenSource = new CancellationTokenSource();
        await cancellationTokenSource.CancelAsync();

        // Act & Assert
        await Should.ThrowAsync<OperationCanceledException>(
            () => _handler.Handle(command, cancellationTokenSource.Token));
    }

    [Fact]
    public async Task Handle_ShouldHandleSchedulerException_WhenSchedulerThrowsException()
    {
        // Arrange
        var userId = 505;
        var paymentWalletId = Guid.NewGuid().ToString();
        var iban = Iban.Of("**************************");
        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId, iban);

        // Setup empty user configs
        _mockUserConfigsDbSet.Setup(x => x.AsTracking()).Returns(_mockUserConfigsDbSet.Object);

        var schedulerException = new SchedulerException("Test exception");
        _schedulerFactoryMock.Setup(x => x.GetScheduler(It.IsAny<CancellationToken>()))
            .ThrowsAsync(schedulerException);

        // Act & Assert
        var exception = await Should.ThrowAsync<SchedulerException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ShouldBe(schedulerException);

        // Verify that data was still saved before the scheduler exception
        _mockUserConfigsDbSet.Verify(x => x.Add(It.Is<UserConfig>(uc =>
            uc.UserId == userId &&
            uc.WalletInformation != null &&
            uc.WalletInformation.PaymentWalletId.Value == Guid.Parse(paymentWalletId))), Times.Once);

        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldNotCreateDuplicateUserConfig_WhenUserConfigAlreadyExists()
    {
        // Arrange
        var userId = 404;
        var originalWalletId = Guid.NewGuid();
        var newWalletId = Guid.NewGuid();

        // Create an existing user config
        var existingWalletInfo = UserWalletInformation.Create(userId, originalWalletId);
        var existingUserConfig = UserConfig.Create(existingWalletInfo, Iban.Of("**************************"));

        // Setup mock to return existing user config
        var existingUserConfigs = new List<UserConfig> { existingUserConfig };
        var mockExistingDbSet = MockDbSetHelper.CreateMockDbSet(existingUserConfigs);
        mockExistingDbSet.Setup(x => x.AsTracking()).Returns(mockExistingDbSet.Object);
        _mockDbContext.Setup(x => x.UserConfigs).Returns(mockExistingDbSet.Object);

        var iban = Iban.Of("**************************");
        var command = new AddUserPaymentWalletCommand(userId, newWalletId.ToString(), iban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        _schedulerFactoryMock.Verify(x => x.GetScheduler(It.IsAny<CancellationToken>()), Times.Once);
        _schedulerMock.Verify(x => x.TriggerJob(It.Is<JobKey>(j => j.Name == nameof(CreateSettlementWalletJob)), It.IsAny<CancellationToken>()), Times.Once);

        // Verify that no new UserConfig was added (should update existing one)
        mockExistingDbSet.Verify(x => x.Add(It.IsAny<UserConfig>()), Times.Never);

        // Verify that the existing wallet information was updated
        existingUserConfig.WalletInformation.PaymentWalletId.Value.ShouldBe(newWalletId);

        // Verify SaveChangesAsync was called
        _mockDbContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
}
