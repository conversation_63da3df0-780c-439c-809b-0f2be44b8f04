using Microsoft.EntityFrameworkCore;
using Moq;
using Quartz;
using Shouldly;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Features.UserConfigurations;
using Zify.Settlement.Application.Features.Wallets.Jobs;

namespace PayPing.Settlement.Application.UnitTests.Features.UserConfigurations.AddUserPaymentWallet;

public class AddUserPaymentWalletCommandHandlerTests : TempDatabase
{
    private readonly AddUserPaymentWalletCommandHandler _handler;
    private readonly Mock<ISchedulerFactory> _schedulerFactoryMock;
    private readonly Mock<IScheduler> _schedulerMock;

    public AddUserPaymentWalletCommandHandlerTests()
    {
        _schedulerMock = new Mock<IScheduler>();
        _schedulerFactoryMock = new Mock<ISchedulerFactory>();
        _schedulerFactoryMock.Setup(x => x.GetScheduler(It.IsAny<CancellationToken>()))
            .ReturnsAsync(_schedulerMock.Object);

        _handler = new AddUserPaymentWalletCommandHandler(Context, _schedulerFactoryMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldCreateNewUserConfigWithWalletInformation_WhenUserConfigDoesNotExist()
    {
        // Arrange
        var userId = 123;
        var paymentWalletId = Guid.NewGuid().ToString();
        var iban = Iban.Of("**************************");
        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId, iban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        _schedulerFactoryMock.Verify(x => x.GetScheduler(It.IsAny<CancellationToken>()), Times.Once);
        _schedulerMock.Verify(x => x.TriggerJob(It.Is<JobKey>(j => j.Name == nameof(CreateSettlementWalletJob)), It.IsAny<CancellationToken>()), Times.Once);

        var userConfig = await Context.UserConfigs
            .Include(x => x.WalletInformation)
            .FirstOrDefaultAsync(x => x.UserId == userId);

        userConfig.ShouldNotBeNull();
        userConfig.UserId.ShouldBe(userId);
        userConfig.WalletInformation.ShouldNotBeNull();
        userConfig.WalletInformation.UserId.ShouldBe(userId);
        userConfig.WalletInformation.PaymentWalletId.Value.ShouldBe(Guid.Parse(paymentWalletId));
    }

    [Fact]
    public async Task Handle_ShouldUpdateExistingWalletInformation_WhenUserConfigExists()
    {
        // Arrange
        var userId = 456;
        var originalWalletId = Guid.NewGuid();
        var newWalletId = Guid.NewGuid();
        var iban = Iban.Of("**************************");

        // Create an existing user config with wallet information
        var existingWalletInfo = UserWalletInformation.Create(userId, originalWalletId);
        var existingUserConfig = UserConfig.Create(existingWalletInfo, Iban.Of("**************************"));

        Context.UserConfigs.Add(existingUserConfig);
        await Context.SaveChangesAsync();

        var command = new AddUserPaymentWalletCommand(userId, newWalletId.ToString(), iban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        _schedulerFactoryMock.Verify(x => x.GetScheduler(It.IsAny<CancellationToken>()), Times.Once);
        _schedulerMock.Verify(x => x.TriggerJob(It.Is<JobKey>(j => j.Name == nameof(CreateSettlementWalletJob)), It.IsAny<CancellationToken>()), Times.Once);

        var userConfig = await Context.UserConfigs
            .Include(x => x.WalletInformation)
            .FirstOrDefaultAsync(x => x.UserId == userId);

        userConfig.ShouldNotBeNull();
        userConfig.WalletInformation.PaymentWalletId.Value.ShouldBe(newWalletId);
        userConfig.WalletInformation.PaymentWalletId.Value.ShouldNotBe(originalWalletId);
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenInvalidWalletIdProvided()
    {
        // Arrange
        var userId = 789;
        var invalidWalletId = "invalid-guid";
        var iban = Iban.Of("**************************");

        var command = new AddUserPaymentWalletCommand(userId, invalidWalletId, iban);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidWalletIdException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenEmptyGuidProvided()
    {
        // Arrange
        var userId = 101;
        var emptyGuid = Guid.Empty.ToString();
        var iban = Iban.Of("**************************");

        var command = new AddUserPaymentWalletCommand(userId, emptyGuid, iban);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidWalletIdException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ShouldCreateUserConfigWithDefaultValues_WhenCreatingNew()
    {
        // Arrange
        var userId = 202;
        var paymentWalletId = Guid.NewGuid().ToString();
        var iban = Iban.Of("**************************");

        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId, iban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        _schedulerFactoryMock.Verify(x => x.GetScheduler(It.IsAny<CancellationToken>()), Times.Once);
        _schedulerMock.Verify(x => x.TriggerJob(It.Is<JobKey>(j => j.Name == nameof(CreateSettlementWalletJob)), It.IsAny<CancellationToken>()), Times.Once);

        var userConfig = await Context.UserConfigs
            .Include(x => x.WalletInformation)
            .FirstOrDefaultAsync(x => x.UserId == userId);

        userConfig.ShouldNotBeNull();
        userConfig.AcceptorCode.ShouldBe(string.Empty);
        userConfig.IsCritical.ShouldBeFalse();
        userConfig.IsFree.ShouldBeFalse();
        userConfig.IsDepositActivate.ShouldBeFalse();
        userConfig.IsBanned.ShouldBeFalse();
        userConfig.WageType.ShouldBe(WageType.Fixed);
        userConfig.WageValue.ShouldBe(0);
        userConfig.Max.ShouldBe(0);
        userConfig.Min.ShouldBe(0);
        userConfig.MaxSettlementAmount.ShouldBe(0);
        userConfig.PlanType.ShouldBe(SettlementPlanType.Basic);
        userConfig.AllowSettlementRegistration.ShouldBeFalse();
        userConfig.AuthenticatorTotpEnabled.ShouldBeFalse();
        userConfig.AuthenticatorTotpSecretKey.ShouldBe(string.Empty);
    }

    [Fact]
    public async Task Handle_ShouldHandleCancellationToken_WhenCancellationRequested()
    {
        // Arrange
        var userId = 303;
        var paymentWalletId = Guid.NewGuid().ToString();
        var iban = Iban.Of("**************************");

        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId, iban);

        var cancellationTokenSource = new CancellationTokenSource();
        await cancellationTokenSource.CancelAsync();

        // Act & Assert
        await Should.ThrowAsync<OperationCanceledException>(
            () => _handler.Handle(command, cancellationTokenSource.Token));
    }

    [Fact]
    public async Task Handle_ShouldHandleSchedulerException_WhenSchedulerThrowsException()
    {
        // Arrange
        var userId = 505;
        var paymentWalletId = Guid.NewGuid().ToString();
        var iban = Iban.Of("**************************");
        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId, iban);

        var schedulerException = new SchedulerException("Test exception");
        _schedulerFactoryMock.Setup(x => x.GetScheduler(It.IsAny<CancellationToken>()))
            .ThrowsAsync(schedulerException);

        // Act & Assert
        var exception = await Should.ThrowAsync<SchedulerException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ShouldBe(schedulerException);

        // Verify that data was still saved
        var userConfig = await Context.UserConfigs
            .Include(x => x.WalletInformation)
            .FirstOrDefaultAsync(x => x.UserId == userId);

        userConfig.ShouldNotBeNull();
        userConfig.WalletInformation.ShouldNotBeNull();
        userConfig.WalletInformation.PaymentWalletId.Value.ShouldBe(Guid.Parse(paymentWalletId));
    }

    [Fact]
    public async Task Handle_ShouldNotCreateDuplicateUserConfig_WhenUserConfigAlreadyExists()
    {
        // Arrange
        var userId = 404;
        var originalWalletId = Guid.NewGuid();
        var newWalletId = Guid.NewGuid();

        // Create an existing user config
        var existingWalletInfo = UserWalletInformation.Create(userId, originalWalletId);
        var existingUserConfig = UserConfig.Create(existingWalletInfo, Iban.Of("**************************"));

        Context.UserConfigs.Add(existingUserConfig);
        await Context.SaveChangesAsync();

        var iban = Iban.Of("**************************");
        var command = new AddUserPaymentWalletCommand(userId, newWalletId.ToString(), iban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        _schedulerFactoryMock.Verify(x => x.GetScheduler(It.IsAny<CancellationToken>()), Times.Once);
        _schedulerMock.Verify(x => x.TriggerJob(It.Is<JobKey>(j => j.Name == nameof(CreateSettlementWalletJob)), It.IsAny<CancellationToken>()), Times.Once);

        var userConfigs = await Context.UserConfigs
            .Where(x => x.UserId == userId)
            .ToListAsync();

        userConfigs.Count.ShouldBe(1); // Should not create duplicate
    }
}
