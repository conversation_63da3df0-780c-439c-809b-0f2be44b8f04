﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Zify.Settlement.Application.Infrastructure.Configurations;

public sealed class ServiceDiscoveryOptions
{
    public string InquiryServiceAddress { get; set; } = null!;

    [ConfigurationKeyName("Wallet_Grpc_Address")]
    public string WalletGrpcAddress { get; set; } = null!;
}