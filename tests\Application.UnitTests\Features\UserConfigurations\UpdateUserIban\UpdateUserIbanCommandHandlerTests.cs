using Microsoft.EntityFrameworkCore;
using Moq;
using PayPing.Settlement.Application.UnitTests.Infrastructure;
using Shouldly;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Features.UserConfigurations;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace PayPing.Settlement.Application.UnitTests.Features.UserConfigurations.UpdateUserIban;

public class UpdateUserIbanCommandHandlerTests
{
    private readonly Mock<ICurrentUserService> _mockCurrentUserService;
    private readonly Mock<IDateTime> _mockDateTime;
    private readonly UpdateUserIbanCommandHandler _handler;

    public UpdateUserIbanCommandHandlerTests()
    {
        _mockCurrentUserService = new Mock<ICurrentUserService>();
        _mockDateTime = new Mock<IDateTime>();
        _handler = new UpdateUserIbanCommandHandler(Context, _mockCurrentUserService.Object, _mockDateTime.Object);
    }

    private async Task<UserConfig> CreateUserConfigInDatabase(int userId)
    {
        var walletInfo = UserWalletInformation.Create(userId, Guid.NewGuid());
        var userConfig = UserConfig.Create(walletInfo, Iban.Of("**************************"));

        Context.UserConfigs.Add(userConfig);
        await Context.SaveChangesAsync();

        return userConfig;
    }

    [Fact]
    public async Task Handle_ShouldReturnSuccess_WhenValidIbanAndUnderRateLimit()
    {
        // Arrange
        var userId = 123;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        await CreateUserConfigInDatabase(userId);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var updatedUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId);

        updatedUserConfig.ShouldNotBeNull();
        updatedUserConfig.Iban.Value.ShouldBe(newIban);
        updatedUserConfig.IbanChangeHistory.ShouldContain(currentTime);
    }

    [Fact]
    public async Task Handle_ShouldReturnNotFound_WhenUserConfigDoesNotExist()
    {
        // Arrange
        var userId = 999;
        var newIban = "**************************";

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(DateTime.UtcNow);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorOr.ErrorType.NotFound);
        result.FirstError.Description.ShouldBe("اطلاعات کاربری یافت نشد.");
    }

    [Fact]
    public async Task Handle_ShouldReturnForbidden_WhenRateLimitExceeded()
    {
        // Arrange
        var userId = 456;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = await CreateUserConfigInDatabase(userId);

        // Add a change within the time window to exceed the rate limit (default is 1 per 24 hours)
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-12));
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorOr.ErrorType.Forbidden);
        result.FirstError.Description.ShouldContain("شما در هر");
        result.FirstError.Description.ShouldContain("ساعت تنها");
        result.FirstError.Description.ShouldContain("بار می‌توانید");
        result.FirstError.Description.ShouldContain("شماره شبا");
    }

    [Fact]
    public async Task Handle_ShouldIncludeConfigurationValuesInErrorMessage_WhenRateLimitExceeded()
    {
        // Arrange
        var userId = 789;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = await CreateUserConfigInDatabase(userId);

        // Add a change to exceed the rate limit
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-12));
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();

        // The error message should contain the configuration values
        // Default values: 24 hours window, 1 max change
        result.FirstError.Description.ShouldContain("24");
        result.FirstError.Description.ShouldContain("1");
    }

    [Fact]
    public async Task Handle_ShouldNotUpdateDatabase_WhenIbanIsSame()
    {
        // Arrange
        var userId = 321;
        var currentTime = DateTime.UtcNow;

        var userConfig = await CreateUserConfigInDatabase(userId);
        var originalIban = userConfig.Iban.Value;
        var originalHistoryCount = userConfig.IbanChangeHistory.Count;

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(originalIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var updatedUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId);

        updatedUserConfig.ShouldNotBeNull();
        updatedUserConfig.Iban.Value.ShouldBe(originalIban);
        updatedUserConfig.IbanChangeHistory.Count.ShouldBe(originalHistoryCount); // Should not change
    }

    [Fact]
    public async Task Handle_ShouldAllowUpdate_WhenPreviousChangesAreOutsideTimeWindow()
    {
        // Arrange
        var userId = 654;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = await CreateUserConfigInDatabase(userId);

        // Add changes outside the 24-hour window
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-25));
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-30));
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var updatedUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId);

        updatedUserConfig.ShouldNotBeNull();
        updatedUserConfig.Iban.Value.ShouldBe(newIban);
        updatedUserConfig.IbanChangeHistory.ShouldContain(currentTime);
    }

    [Fact]
    public async Task Handle_ShouldCleanUpOldHistoryEntries_WhenUpdating()
    {
        // Arrange
        var userId = 987;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        var userConfig = await CreateUserConfigInDatabase(userId);

        // Add old entries that should be cleaned up
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-25));
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-30));
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-48));
        await Context.SaveChangesAsync();

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var updatedUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId);

        updatedUserConfig.ShouldNotBeNull();
        updatedUserConfig.IbanChangeHistory.Count.ShouldBe(1); // Only the new entry should remain
        updatedUserConfig.IbanChangeHistory.ShouldContain(currentTime);
    }

    [Theory]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    public async Task Handle_ShouldAcceptValidIranianIbans_WhenUnderRateLimit(string validIban)
    {
        // Arrange
        var userId = 555;
        var currentTime = DateTime.UtcNow;

        await CreateUserConfigInDatabase(userId);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(validIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var updatedUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId);

        updatedUserConfig.ShouldNotBeNull();
        updatedUserConfig.Iban.Value.ShouldBe(validIban);
    }

    [Fact]
    public async Task Handle_ShouldPersistChangesToDatabase_WhenSuccessful()
    {
        // Arrange
        var userId = 111;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        await CreateUserConfigInDatabase(userId);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify changes are persisted by refreshing the context
        Context.ChangeTracker.Clear();
        var persistedUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId);

        persistedUserConfig.ShouldNotBeNull();
        persistedUserConfig.Iban.Value.ShouldBe(newIban);
    }

    [Fact]
    public async Task Handle_ShouldNotModifyOtherUsers_WhenUpdatingIban()
    {
        // Arrange
        var userId1 = 222;
        var userId2 = 333;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        await CreateUserConfigInDatabase(userId1);
        var userConfig2 = await CreateUserConfigInDatabase(userId2);
        var originalIban2 = userConfig2.Iban.Value;

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId1);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var updatedUserConfig1 = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId1);
        var updatedUserConfig2 = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId2);

        updatedUserConfig1.ShouldNotBeNull();
        updatedUserConfig1.Iban.Value.ShouldBe(newIban);

        updatedUserConfig2.ShouldNotBeNull();
        updatedUserConfig2.Iban.Value.ShouldBe(originalIban2); // Should remain unchanged
    }

    [Fact]
    public async Task Handle_ShouldUseCurrentUserServiceUserId_WhenFindingUserConfig()
    {
        // Arrange
        var actualUserId = 777;
        var differentUserId = 888;
        var newIban = "**************************";
        var currentTime = DateTime.UtcNow;

        await CreateUserConfigInDatabase(actualUserId);
        await CreateUserConfigInDatabase(differentUserId);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(actualUserId);
        _mockDateTime.Setup(x => x.Now).Returns(currentTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        // Verify only the correct user's IBAN was updated
        var updatedUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == actualUserId);
        var otherUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == differentUserId);

        updatedUserConfig.ShouldNotBeNull();
        updatedUserConfig.Iban.Value.ShouldBe(newIban);

        otherUserConfig.ShouldNotBeNull();
        otherUserConfig.Iban.Value.ShouldNotBe(newIban);
    }

    [Fact]
    public async Task Handle_ShouldUseDateTimeServiceNow_WhenUpdatingIban()
    {
        // Arrange
        var userId = 999;
        var newIban = "**************************";
        var specificTime = new DateTime(2024, 1, 15, 10, 30, 45, DateTimeKind.Utc);

        await CreateUserConfigInDatabase(userId);

        _mockCurrentUserService.Setup(x => x.UserId).Returns(userId);
        _mockDateTime.Setup(x => x.Now).Returns(specificTime);

        var command = new UpdateUserIbanCommand(newIban);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var updatedUserConfig = await Context.UserConfigs
            .FirstOrDefaultAsync(uc => uc.UserId == userId);

        updatedUserConfig.ShouldNotBeNull();
        updatedUserConfig.IbanChangeHistory.ShouldContain(specificTime);
    }
}
