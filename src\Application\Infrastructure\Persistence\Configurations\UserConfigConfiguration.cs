using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;

public class UserConfigConfiguration : IEntityTypeConfiguration<UserConfig>
{
    public void Configure(EntityTypeBuilder<UserConfig> builder)
    {
        builder.ToTable("UserConfigs");

        builder.<PERSON><PERSON>ey(uc => uc.Id);

        builder.Property(uc => uc.Id)
               .ValueGeneratedOnAdd();

        builder.Property(uc => uc.UserId)
               .IsRequired();

        builder.ComplexProperty(
            uc => uc.Iban, buildAction =>
            {
                buildAction.Property(p => p.Value)
                    .HasColumnName("Iban")
                    .IsRequired();
            });

        builder.Property(uc => uc.AcceptorCode)
               .IsRequired()
               .HasMaxLength(100);

        builder.Property(uc => uc.IsCritical)
               .IsRequired();

        builder.Property(uc => uc.IsFree)
               .IsRequired();

        builder.Property(uc => uc.IsDepositActivate)
               .IsRequired();

        builder.Property(uc => uc.IsBanned)
               .IsRequired();

        builder.Property(uc => uc.WageType)
               .IsRequired();

        builder.Property(uc => uc.WageValue)
               .IsRequired()
               .HasPrecision(18, 2);

        builder.Property(uc => uc.DailyTransferLimit)
               .IsRequired()
               .HasPrecision(18, 2);

        builder.Property(uc => uc.Max)
               .IsRequired();

        builder.Property(uc => uc.Min)
               .IsRequired();

        builder.Property(uc => uc.MaxSettlementAmount)
               .IsRequired();

        builder.Property(uc => uc.PlanType)
               .IsRequired();

        builder.Property(uc => uc.AllowSettlementRegistration)
               .IsRequired();

        builder.Property(uc => uc.AuthenticatorTotpEnabled)
               .IsRequired();

        builder.Property(uc => uc.AuthenticatorTotpSecretKey)
               .IsRequired(false)
               .HasMaxLength(200);

        builder.Property(uc => uc.IbanChangeHistory)
               .HasConversion(
                   v => string.Join(';', v.Select(dt => dt.ToBinary())),
                   v => v.Split(';', StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => DateTime.FromBinary(long.Parse(s)))
                        .ToList())
               .HasColumnName("IbanChangeHistory")
               .IsRequired(false);

        builder.HasOne(uc => uc.WalletInformation)
               .WithOne()
               .HasForeignKey<UserWalletInformation>(wi => wi.UserConfigId)
               .IsRequired();
    }
}