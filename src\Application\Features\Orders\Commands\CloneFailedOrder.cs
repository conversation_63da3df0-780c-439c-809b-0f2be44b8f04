using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders.Commands;

public sealed record CloneFailedOrderResponse(
    Guid NewOrderId,
    IReadOnlyList<CloneOrderDetailResponse> NewOrderDetails)
{
    public static CloneFailedOrderResponse FromOrder(Order order)
    {
        var newOrderDetails = order.OrderDetails
            .Select(od => new CloneOrderDetailResponse(
                od.Id,
                od.Amount,
                od.WageAmount,
                od.Iban,
                od.Description))
            .ToList();
        return new CloneFailedOrderResponse(order.Id, newOrderDetails);
    }
}

public sealed record CloneOrderDetailResponse(Guid Id,
    decimal Amount,
    decimal Wage,
    Iban Iban,
    string Description);

public sealed class CloneFailedOrderController : ApiControllerBase
{
    /// <summary>
    /// Clones a failed order by updating the original order status to Fail and creating an exact copy with a new ID.
    /// </summary>
    /// <param name="orderId">The unique identifier of the order to be cloned.</param>
    /// <returns>
    /// An <see cref="IActionResult"/> containing the result of the operation:
    /// - A <see cref="CloneFailedOrderResponse"/> with the new and original order IDs if successful.
    /// - A <see cref="ProblemDetails"/> object if the request is invalid or an error occurs.
    /// </returns>
    /// <remarks>
    /// This endpoint requires authorization with the "write" policy.
    /// The original order must be in a status that allows it to be failed (WalletProcessing, Processing, or Submitted).
    /// </remarks>
    [HttpPost("{orderId:guid}/clone-failed")]
    [Authorize("write")]
    [ProducesResponseType<CloneFailedOrderResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CloneFailedOrder([FromRoute] Guid orderId)
    {
        var command = new CloneFailedOrderCommand(orderId);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);

        return result.Match(Ok, Problem);
    }
}

public sealed record CloneFailedOrderCommand(Guid OrderId)
    : IRequest<CloneFailedOrderCommand, Task<ErrorOr<CloneFailedOrderResponse>>>;

public sealed class CloneFailedOrderCommandHandler(
    ApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<CloneFailedOrderCommand, Task<ErrorOr<CloneFailedOrderResponse>>>
{
    public async Task<ErrorOr<CloneFailedOrderResponse>> Handle(
        CloneFailedOrderCommand request,
        CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser == null)
            return Error.Unauthorized("شما مجوز انجام این عملیات را ندارید.");

        // Retrieve the original order with its details
        var originalOrder = await dbContext.Orders
            .AsTracking()
            .Include(o => o.OrderDetails)
            .Where(o => o.Id == request.OrderId)
            .FirstOrDefaultAsync(cancellationToken);

        if (originalOrder is null)
            return Error.NotFound(description: "درخواست مورد نظر یافت نشد.");

        // Validate that the order can be failed (business logic check)
        if (!CanOrderBeFailed(originalOrder.Status))
            return Error.Validation(
                code: "InvalidOrderStatus",
                description: $"درخواست با وضعیت {originalOrder.Status} قابل تبدیل به ناموفق نیست.");

        await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            // Update original order status to Fail
            originalOrder.Fail();

            // Clone the order
            var clonedOrder = originalOrder.Clone();

            // Add the cloned order to the database
            dbContext.Orders.Add(clonedOrder);

            // Save changes
            var result = await dbContext.SaveChangesAsync(cancellationToken);

            if (result <= 0)
            {
                await transaction.RollbackAsync(cancellationToken);
                return Error.Failure(description: "خطا در ثبت اطلاعات");
            }

            await transaction.CommitAsync(cancellationToken);

            return CloneFailedOrderResponse.FromOrder(clonedOrder);
        }
        catch (InvalidOperationException ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            return Error.Validation(
                code: "BusinessRuleViolation",
                description: ex.Message);
        }
        catch (Exception)
        {
            await transaction.RollbackAsync(cancellationToken);
            return Error.Failure(description: "خطای غیرمنتظره در پردازش درخواست");
        }
    }

    private static bool CanOrderBeFailed(OrderStatus status)
    {
        OrderStatus[] notAllowedStatuses =
            [OrderStatus.WalletProcessing, OrderStatus.Processing, OrderStatus.Submitted];
        return !notAllowedStatuses.Contains(status);
    }
}

public sealed class CloneFailedOrderCommandValidator : AbstractValidator<CloneFailedOrderCommand>
{
    public CloneFailedOrderCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("شناسه درخواست اجباری می‌باشد.");
    }
}
