using Microsoft.EntityFrameworkCore;
using System.Reflection;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence;

/// <summary>
/// Interface for the application database context to enable proper unit testing with mocking
/// </summary>
public interface IApplicationDbContext
{
    /// <summary>
    /// Gets the Orders DbSet
    /// </summary>
    DbSet<Order> Orders { get; }

    /// <summary>
    /// Gets the OrderDetails DbSet
    /// </summary>
    DbSet<OrderDetail> OrderDetails { get; }

    /// <summary>
    /// Gets the OrderDetailRollbackInfos DbSet
    /// </summary>
    DbSet<OrderDetailRollbackInfo> OrderDetailRollbackInfos { get; }

    /// <summary>
    /// Gets the UserConfigs DbSet
    /// </summary>
    DbSet<UserConfig> UserConfigs { get; }

    /// <summary>
    /// Gets the UserWalletInformations DbSet
    /// </summary>
    DbSet<UserWalletInformation> UserWalletInformations { get; }

    /// <summary>
    /// Saves all changes made in this context to the database
    /// </summary>
    /// <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete</param>
    /// <returns>The number of state entries written to the database</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
public sealed class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : DbContext(options), IApplicationDbContext
{
    //private DbSet<OutboxEntity> Outboxes { get; set; } = null!;
    public DbSet<Order> Orders => Set<Order>();
    public DbSet<OrderDetail> OrderDetails => Set<OrderDetail>();
    public DbSet<OrderDetailRollbackInfo> OrderDetailRollbackInfos => Set<OrderDetailRollbackInfo>();
    public DbSet<UserConfig> UserConfigs => Set<UserConfig>();
    public DbSet<UserWalletInformation> UserWalletInformations => Set<UserWalletInformation>();

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // Configure AuditableEntity properties for all derived entities
        var mutableEntityTypes = builder.Model
            .GetEntityTypes().Where(e => typeof(AuditableEntity).IsAssignableFrom(e.ClrType));
        foreach (var entityType in mutableEntityTypes)
        {
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.Version)).IsRowVersion();
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.Created)).IsRequired();
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.CreatedBy)).IsRequired(false);
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.LastModified)).IsRequired(false);
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.LastModifiedBy)).IsRequired(false);
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.IsDeleted)).IsRequired().HasDefaultValue(false);
            builder.Entity(entityType.ClrType).Property(nameof(AuditableEntity.DeletedOn)).IsRequired(false);
        }
    }
}
