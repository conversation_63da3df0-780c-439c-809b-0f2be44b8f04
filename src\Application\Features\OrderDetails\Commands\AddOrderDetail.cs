﻿using DispatchR.Requests.Send;
using DNTPersianUtils.Core;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Domain.Helpers;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.OrderDetails.Commands;

public sealed record AddOrderDetailResponse(OrderDetailResponse OrderDetail);

public record OrderDetailResponse(
    Guid Id,
    decimal Amount,
    decimal Wage,
    Iban Iban,
    string FullName,
    string Description)
{
    public static OrderDetailResponse FromDomain(OrderDetail orderDetail, string fullName) =>
        new(orderDetail.Id,
            orderDetail.Amount,
            orderDetail.WageAmount,
            orderDetail.Iban,
            fullName,
            orderDetail.Description);
};

public sealed class AddOrderDetailController : ApiControllerBase
{
    /// <summary>
    /// Adds a new order detail to the specified order.
    /// </summary>
    /// <param name="orderId">
    /// The unique identifier of the order to which the detail will be added.
    /// </param>
    /// <param name="request">
    /// The command containing the details of the order to be added, including amount, IBAN, description, mobile, and national ID.
    /// </param>
    /// <returns>
    /// An <see cref="IActionResult"/> representing the result of the operation. 
    /// Returns <see cref="StatusCodes.Status200OK"/> if the operation is successful, 
    /// or <see cref="StatusCodes.Status400BadRequest"/> if the request is invalid.
    /// </returns>
    /// <remarks>
    /// This method requires the "write" authorization policy.
    /// </remarks>
    [HttpPost("{orderId:guid}/add-order-detail")]
    [Authorize("write")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddOrderDetail(
        [FromRoute] Guid orderId,
        [FromBody] AddOrderDetailCommand request)
    {
        request.OrderId = orderId;
        var result = await Mediator.Send(request, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record AddOrderDetailCommand(
    decimal Amount,
    string Iban,
    string? Description,
    string? Mobile,
    string? NationalId)
    : IRequest<AddOrderDetailCommand, Task<ErrorOr<AddOrderDetailResponse>>>
{
    [JsonIgnore]
    public Guid OrderId { get; set; }
}

public sealed class AddOrderDetailCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    IDateTime dateTime,
    IEzPayService ezPayService)
    : IRequestHandler<AddOrderDetailCommand, Task<ErrorOr<AddOrderDetailResponse>>>
{
    public async Task<ErrorOr<AddOrderDetailResponse>> Handle(
        AddOrderDetailCommand request,
        CancellationToken cancellationToken)
    {
        var order = await dbContext.Orders
            .AsTracking()
            .Where(o => o.Id == request.OrderId)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "کد درخواست نامعتبر می‌باشد");

        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست افزودن تسویه امکان پذیر نیست");

        var fullNameOrIban = await TryValidateIbanAndAccountStatus(request.Iban);
        if (fullNameOrIban.IsError)
            return fullNameOrIban.Errors;

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null) return Error.NotFound(description: "اطلاعات کاربری یافت نشد");

        var exceedLimitation = await ExceedDailyTransferLimitation(userConfig, request.Amount, cancellationToken);
        if (exceedLimitation.IsError)
            return exceedLimitation.Errors;

        var orderDetail = OrderDetail.Create(
            Iban.Of(request.Iban),
            request.Amount,
            userConfig.CalculateWage(request.Amount),
            request.NationalId,
            request.Mobile,
            request.Description);

        order.AddDetail(orderDetail);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0
            ? new AddOrderDetailResponse(OrderDetailResponse.FromDomain(orderDetail, fullNameOrIban.Value))
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }

    private async Task<ErrorOr<string>> TryValidateIbanAndAccountStatus(string iban)
    {
        var inquiryIban = await ezPayService.InquiryIban(iban);
        var fullNameOrIban = inquiryIban.Value?.AccountOwners.FirstOrDefault()?.FullName ?? iban;

        if (!inquiryIban.IsError && (!inquiryIban.Value?.IsActive ?? true))
        {
            return Error.Forbidden(description:
                $"حساب بانکی مربوط به شبای {fullNameOrIban} مسدود می‌باشد");
        }

        return fullNameOrIban;
    }

    private async Task<ErrorOr<Success>> ExceedDailyTransferLimitation(UserConfig userConfig, decimal amount, CancellationToken ct)
    {
        OrderDetailStatus[] statuses = [OrderDetailStatus.InProgress, OrderDetailStatus.Success];

        var totalAmount = await dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.CreatedBy == currentUserService.UserId)
            .Where(x => x.Created >= dateTime.Now.Date && x.Created < dateTime.Now.AddDays(1).Date)
            .Where(x => statuses.Contains(x.Status))
            .SumAsync(x => x.Amount, ct);

        if (totalAmount + amount > userConfig.DailyTransferLimit)
            return Error.Conflict(description:
                string.Format(
                    ErrorMessages.MessageFormats.DailyTransferLimitExceeded,
                    amount.ToString("N0"),
                    userConfig.DailyTransferLimit.ToString("N0"),
                    totalAmount.ToString("N0")
                ));

        return Result.Success;
    }
}

public sealed class AddOrderDetailCommandValidator : AbstractValidator<AddOrderDetailCommand>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;
    private readonly IDateTime _dateTime;

    public AddOrderDetailCommandValidator(
        ApplicationDbContext dbContext,
        ICurrentUserService currentUserService,
        IDateTime dateTime)
    {
        _dbContext = dbContext;
        _currentUserService = currentUserService;
        _dateTime = dateTime;

        WhenAsync(UserHaSimplePlan,
            () => throw new SimplePlanSettlementAdditionForbiddenException());

        WhenAsync(ReachedMaximumOrderDetailsCount,
            () => throw new ReachedMaximumOrderDetailsCountException(UserOptions.GetMaxSettlementCountPerRequest));

        RuleFor(x => x.Amount)
            .NotEmpty().WithMessage("مبلغ اجباری است")
            .GreaterThan(UserOptions.GetMinSettlementAmount)
            .WithMessage("مبلغ تسویه کمتر از حد مجاز")
            .MustAsync(LessThanMaxSettlementAmount)
            .WithMessage("مبلغ تسویه بیشتر از حد مجاز");

        RuleFor(x => x.Iban).NotEmpty();

        RuleFor(x => x.OrderId).NotEmpty();

        RuleFor(x => x.Description)
            .MaximumLength(50).WithMessage("متن توضیحات بیشتر از ۵۰ کارکتر نمی‌تواند باشد");

        WhenAsync(UserIsCritical, () =>
        {
            RuleFor(x => x.Mobile)
                .NotEmpty().WithMessage(ErrorMessages.MobileNumberRequired)
                .Must(ValidIranianMobileNumber).WithMessage(ErrorMessages.InvalidNationalCode);

            RuleFor(x => x.NationalId)
                .NotEmpty().WithMessage(ErrorMessages.NationalCodeRequired)
                .Must(ValidIranianNationalCode).WithMessage(ErrorMessages.InvalidNationalCode);

            RuleFor(x => x.Iban)
                .MustAsync(LessThanMaxTransferLimit).WithMessage(x =>
                    $"امکان واریز بیش از 100میلیون تومان در ۲۴ساعت برای این شبا {x.Iban} نمی‌باشد");
        });
    }

    private async Task<bool> UserHaSimplePlan(AddOrderDetailCommand _, CancellationToken ct)
    {
        var plan = await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == _currentUserService.UserId)
            .Select(x => x.PlanType)
            .FirstOrDefaultAsync(ct);

        return plan == SettlementPlanType.Basic;
    }

    private async Task<bool> ReachedMaximumOrderDetailsCount(AddOrderDetailCommand request, CancellationToken ct)
    {
        var detailsCount = await _dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.OrderId == request.OrderId)
            .CountAsync(ct);

        return detailsCount >= UserOptions.GetMaxSettlementCountPerRequest;
    }

    private async Task<bool> LessThanMaxSettlementAmount(decimal amount, CancellationToken ct)
    {
        long max = await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == _currentUserService.UserId)
            .Select(x => x.MaxSettlementAmount)
            .FirstOrDefaultAsync(ct);
        if (max == 0) max = UserOptions.GetMaxSettlementAmount;
        return amount <= max;
    }

    private async Task<bool> UserIsCritical(AddOrderDetailCommand _, CancellationToken ct)
    {
        return await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == _currentUserService.UserId)
            .Select(x => x.IsCritical)
            .FirstOrDefaultAsync(ct);
    }

    private async Task<bool> LessThanMaxTransferLimit(AddOrderDetailCommand request, string iban, CancellationToken ct)
    {
        OrderDetailStatus[] statuses = [OrderDetailStatus.InProgress, OrderDetailStatus.Success];
        var totalTransferAmount = await _dbContext.OrderDetails
            .AsNoTracking()
            .Where(x => x.CreatedBy == _currentUserService.UserId)
            .Where(x => x.Iban == iban)
            .Where(x => x.Created >= _dateTime.Now.Date && x.Created < _dateTime.Now.AddDays(1).Date)
            .Where(x => statuses.Contains(x.Status))
            .SumAsync(x => x.Amount, ct);

        return totalTransferAmount + request.Amount <= UserOptions.GetMaxLast24Amount;
    }

    private static bool ValidIranianMobileNumber(string? mobileNumber) => mobileNumber.IsValidIranianMobileNumber();
    private static bool ValidIranianNationalCode(string? nationalCode) => nationalCode.IsValidIranianNationalCode();
}

public class SimplePlanSettlementAdditionForbiddenException()
    : Exception(message: ErrorMessages.SimplePlanSettlementAdditionForbidden);