using ErrorOr;
using Microsoft.EntityFrameworkCore;
using Moq;
using Shouldly;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Features.Orders.Commands;

namespace PayPing.Settlement.Application.UnitTests.Features.Orders.Commands;

public class CloneFailedOrderTests : TempDatabase
{
    private readonly Mock<ICurrentUserService> _currentUserServiceMock;
    private readonly CloneFailedOrderCommandHandler _handler;

    public CloneFailedOrderTests()
    {
        _currentUserServiceMock = new Mock<ICurrentUserService>();
        _handler = new CloneFailedOrderCommandHandler(Context, _currentUserServiceMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnUnauthorized_WhenUserIsNotAuthenticated()
    {
        // Arrange
        _currentUserServiceMock.Setup(x => x.UserId).Returns((int?)null);
        var command = new CloneFailedOrderCommand(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.Unauthorized);
        // The error message might be transformed by middleware, so just check that it's an unauthorized error
    }

    [Fact]
    public async Task Handle_ShouldReturnNotFound_WhenOrderDoesNotExist()
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);
        var nonExistentOrderId = Guid.NewGuid();
        var command = new CloneFailedOrderCommand(nonExistentOrderId);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.NotFound);
        result.FirstError.Description.ShouldBe("درخواست مورد نظر یافت نشد.");
    }

    [Theory]
    [InlineData(OrderStatus.Draft)]
    [InlineData(OrderStatus.Completed)]
    [InlineData(OrderStatus.Cancelled)]
    [InlineData(OrderStatus.Failed)]
    public async Task Handle_ShouldReturnValidationError_WhenOrderStatusIsInvalid(OrderStatus invalidStatus)
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);

        var order = Order.Create("Test Order", "Test Description");

        // Set order to invalid status
        if (invalidStatus == OrderStatus.Cancelled)
        {
            order.Cancel();
        }
        else if (invalidStatus == OrderStatus.Completed)
        {
            order.WalletProcessing();
            order.Submit();
            order.Process();
            order.Complete();
        }
        else if (invalidStatus == OrderStatus.Failed)
        {
            order.WalletProcessing();
            order.Fail();
        }
        // Draft is the default status

        Context.Orders.Add(order);
        await Context.SaveChangesAsync();

        var command = new CloneFailedOrderCommand(order.Id);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.Validation);
        result.FirstError.Code.ShouldBe("InvalidOrderStatus");
        result.FirstError.Description.ShouldContain($"درخواست با وضعیت {invalidStatus} قابل تبدیل به ناموفق نیست.");
    }

    [Theory]
    [InlineData(OrderStatus.WalletProcessing)]
    [InlineData(OrderStatus.Processing)]
    [InlineData(OrderStatus.Submitted)]
    public async Task Handle_ShouldSuccessfullyCloneOrder_WhenOrderStatusIsValid(OrderStatus validStatus)
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);

        var order = Order.Create("Test Order", "Test Description");
        var iban = Iban.Of("**************************");
        var detail1 = OrderDetail.Create(iban, 100m, 10m, "1234567890", "09123456789", "Detail 1");
        var detail2 = OrderDetail.Create(iban, 200m, 20m, "0987654321", "09987654321", "Detail 2");

        order.AddDetail(detail1);
        order.AddDetail(detail2);

        // Set order to valid status
        switch (validStatus)
        {
            case OrderStatus.WalletProcessing:
                order.WalletProcessing();
                break;
            case OrderStatus.Processing:
                order.WalletProcessing();
                order.Submit();
                order.Process();
                break;
            case OrderStatus.Submitted:
                order.WalletProcessing();
                order.Submit();
                break;
        }

        Context.Orders.Add(order);
        await Context.SaveChangesAsync();

        var command = new CloneFailedOrderCommand(order.Id);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();
        result.Value.NewOrderId.ShouldNotBe(order.Id);

        // Verify original order is failed
        var updatedOriginalOrder = await Context.Orders
            .Include(o => o.OrderDetails)
            .FirstAsync(o => o.Id == order.Id);
        updatedOriginalOrder.Status.ShouldBe(OrderStatus.Failed);
        updatedOriginalOrder.OrderDetails.ShouldAllBe(d => d.Status == OrderDetailStatus.Failed);

        // Verify cloned order exists and is correct
        var clonedOrder = await Context.Orders
            .Include(o => o.OrderDetails)
            .FirstAsync(o => o.Id == result.Value.NewOrderId);

        clonedOrder.ShouldNotBeNull();
        clonedOrder.Status.ShouldBe(OrderStatus.Draft);
        clonedOrder.Title.ShouldBe(order.Title);
        clonedOrder.Description.ShouldBe(order.Description);
        clonedOrder.OrderDetails.Count.ShouldBe(2);
        clonedOrder.OrderDetails.ShouldAllBe(d => d.Status == OrderDetailStatus.Init);
    }

    [Fact]
    public async Task Handle_ShouldRollbackTransaction_WhenSaveChangesFails()
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);

        var order = Order.Create("Test Order", "Test Description");
        order.WalletProcessing(); // Valid status for failing

        Context.Orders.Add(order);
        await Context.SaveChangesAsync();

        // Dispose the context to simulate save failure
        await Context.DisposeAsync();

        var command = new CloneFailedOrderCommand(order.Id);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.Failure);
    }

    [Fact]
    public async Task Handle_ShouldHandleBusinessRuleViolation_WhenFailMethodThrows()
    {
        // Arrange
        var userId = 123;
        _currentUserServiceMock.Setup(x => x.UserId).Returns(userId);

        var order = Order.Create("Test Order", "Test Description");
        // Order is in Draft status, which should cause Fail() to throw

        Context.Orders.Add(order);
        await Context.SaveChangesAsync();

        var command = new CloneFailedOrderCommand(order.Id);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeTrue();
        result.FirstError.Type.ShouldBe(ErrorType.Validation);
        result.FirstError.Code.ShouldBe("InvalidOrderStatus");
    }


}

public class CloneFailedOrderCommandValidatorTests
{
    private readonly CloneFailedOrderCommandValidator _validator = new();

    [Fact]
    public void Validate_ShouldReturnValid_WhenOrderIdIsProvided()
    {
        // Arrange
        var orderId = Guid.NewGuid();
        var command = new CloneFailedOrderCommand(orderId);

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.ShouldBeTrue();
        result.Errors.Count.ShouldBe(0);
    }

    [Fact]
    public void Validate_ShouldReturnInvalid_WhenOrderIdIsEmpty()
    {
        // Arrange
        var command = new CloneFailedOrderCommand(Guid.Empty);

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.ShouldBeFalse();
        result.Errors.Count.ShouldBe(1);
        result.Errors[0].PropertyName.ShouldBe("Request.OrderId");
        result.Errors[0].ErrorMessage.ShouldBe("شناسه درخواست اجباری می‌باشد.");
    }
}
