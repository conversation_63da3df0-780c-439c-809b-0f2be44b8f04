﻿using Shouldly;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace PayPing.Settlement.Application.UnitTests.Domain;

public class OrderTest
{
    [Fact]
    public void Create_ShouldInitializeOrderWithDefaults_WhenParametersAreNull()
    {
        // Act
        var order = Order.Create();

        // Assert
        order.ShouldNotBeNull();
        order.Status.ShouldBe(OrderStatus.Draft);
        order.Description.ShouldContain("دستور پرداخت");
        order.ScheduledTime.ShouldBeNull();
        order.Id.ShouldNotBe(Guid.Empty);
    }

    [Fact]
    public void Create_ShouldInitializeOrderWithGivenParameters()
    {
        // Arrange
        var description = "Test Order";
        var scheduledTime = DateTimeOffset.Now;

        // Act
        var order = Order.Create(description: description, scheduledTime: scheduledTime);

        // Assert
        order.ShouldNotBeNull();
        order.Status.ShouldBe(OrderStatus.Draft);
        order.Description.ShouldBe(description);
        order.ScheduledTime.ShouldBe(scheduledTime);
        order.Id.ShouldNotBe(Guid.Empty);
    }

    [Theory]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    [InlineData("**************************")]
    public void AddDetail_ShouldAddDetail_WhenValidDetailProvided(string iban)
    {
        // Arrange
        var order = Order.Create();
        var detail = OrderDetail.Create(Iban.Of(iban), 100m, 10m);

        // Act
        order.AddDetail(detail);

        // Assert
        order.OrderDetails.Count.ShouldBe(1);
        order.OrderDetails.ShouldContain(detail);
    }

    [Fact]
    public void AddDetail_ShouldThrow_WhenDetailIsNull()
    {
        // Arrange
        var order = Order.Create();

        // Act & Assert
        Should.Throw<ArgumentNullException>(() => order.AddDetail(null!));
    }

    [Fact]
    public void AddDetail_ShouldThrow_WhenDetailStatusIsNotInit()
    {
        // Arrange
        var order = Order.Create();
        var detail = OrderDetail.Create(Iban.Of("**************************"), 100m, 10m);
        detail.SetStatus(OrderDetailStatus.Success);

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => order.AddDetail(detail));
        exception.Message.ShouldContain("is not in init status");
    }

    [Fact]
    public void AddDetails_ShouldAddAllDetails_WhenValidDetailsProvided()
    {
        // Arrange
        var order = Order.Create();
        var details = new List<OrderDetail>
        {
            OrderDetail.Create(Iban.Of("**************************"), 100m, 10m),
            OrderDetail.Create(Iban.Of("**************************"), 200m, 20m)
        };

        // Act
        order.AddDetails(details);

        // Assert
        order.OrderDetails.Count.ShouldBe(2);
        order.OrderDetails.ShouldContain(details[0]);
        order.OrderDetails.ShouldContain(details[1]);
    }

    [Fact]
    public void AddDetails_ShouldThrow_WhenDetailsListIsNull()
    {
        // Arrange
        var order = Order.Create();

        // Act & Assert
        Should.Throw<ArgumentNullException>(() => order.AddDetails(null!));
    }

    [Theory]
    [InlineData(OrderStatus.WalletProcessing)]
    [InlineData(OrderStatus.Processing)]
    [InlineData(OrderStatus.Submitted)]
    public void Fail_ShouldUpdateStatusToFailed_WhenOrderIsInValidStatus(OrderStatus initialStatus)
    {
        // Arrange
        var order = Order.Create();
        var iban = Iban.Of("**************************");
        var detail = OrderDetail.Create(iban, 100m, 10m);
        order.AddDetail(detail);

        // Set order to the initial status
        switch (initialStatus)
        {
            case OrderStatus.WalletProcessing:
                order.WalletProcessing();
                break;
            case OrderStatus.Processing:
                order.WalletProcessing();
                order.Submit();
                order.Process();
                break;
            case OrderStatus.Submitted:
                order.WalletProcessing();
                order.Submit();
                break;
        }

        // Act
        order.Fail();

        // Assert
        order.Status.ShouldBe(OrderStatus.Failed);
        order.OrderDetails.ShouldAllBe(d => d.Status == OrderDetailStatus.Failed);
    }

    [Theory]
    [InlineData(OrderStatus.Draft)]
    [InlineData(OrderStatus.Completed)]
    [InlineData(OrderStatus.Cancelled)]
    [InlineData(OrderStatus.Failed)]
    public void Fail_ShouldThrowInvalidOperationException_WhenOrderIsInInvalidStatus(OrderStatus invalidStatus)
    {
        // Arrange
        var order = Order.Create();

        // Set order to invalid status (for some we need to manipulate directly since there's no public method)
        if (invalidStatus == OrderStatus.Completed)
        {
            order.WalletProcessing();
            order.Submit();
            order.Process();
            order.Complete();
        }
        else if (invalidStatus == OrderStatus.Cancelled)
        {
            order.Cancel();
        }
        else if (invalidStatus == OrderStatus.Failed)
        {
            order.WalletProcessing();
            order.Fail();
        }
        // Draft is the default status

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => order.Fail());
        exception.Message.ShouldContain($"cannot be failed from status {invalidStatus}");
    }

    [Fact]
    public void WalletProcessing_ShouldUpdateStatusAndAssignWalletId_WhenOrderInDraftStatus()
    {
        // Arrange
        var order = Order.Create();

        // Act
        order.WalletProcessing();

        // Assert
        order.Status.ShouldBe(OrderStatus.WalletProcessing);
        order.WalletBlockCorrelationId.ShouldNotBeNull();
    }

    [Fact]
    public void WalletProcessing_ShouldThrow_WhenOrderNotInDraftStatus()
    {
        // Arrange
        var order = Order.Create();
        order.WalletProcessing();

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => order.WalletProcessing());
        exception.Message.ShouldContain("is not in draft status");
    }

    [Fact]
    public void Submit_ShouldUpdateStatus_WhenOrderInWalletProcessingStatus()
    {
        // Arrange
        var order = Order.Create();
        order.WalletProcessing();

        // Act
        order.Submit();

        // Assert
        order.Status.ShouldBe(OrderStatus.Submitted);
    }

    [Fact]
    public void Submit_ShouldThrow_WhenOrderNotInWalletProcessingStatus()
    {
        // Arrange
        var order = Order.Create();

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => order.Submit());
        exception.Message.ShouldContain("is not in initialized status");
    }

    [Fact]
    public void Cancel_ShouldUpdateStatusToCancelled_WhenOrderInDraftStatus()
    {
        // Arrange
        var order = Order.Create();

        // Act
        order.Cancel();

        // Assert
        order.Status.ShouldBe(OrderStatus.Cancelled);
    }

    [Fact]
    public void Cancel_ShouldThrow_WhenOrderNotInDraftStatus()
    {
        // Arrange
        var order = Order.Create();
        order.WalletProcessing();

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => order.Cancel());
        exception.Message.ShouldContain("can not be cancelled");
    }

    [Fact]
    public void Process_ShouldUpdateStatus_WhenOrderInSubmittedStatus()
    {
        // Arrange
        var order = Order.Create();
        order.WalletProcessing();
        order.Submit();

        // Act
        order.Process();

        // Assert
        order.Status.ShouldBe(OrderStatus.Processing);
    }

    [Fact]
    public void Process_ShouldThrow_WhenOrderNotInSubmittedStatus()
    {
        // Arrange
        var order = Order.Create();

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => order.Process());
        exception.Message.ShouldContain("is not in submitted status");
    }

    [Fact]
    public void Complete_ShouldUpdateStatus_WhenOrderInProcessingStatus()
    {
        // Arrange
        var order = Order.Create();
        order.WalletProcessing();
        order.Submit();
        order.Process();

        // Act
        order.Complete();

        // Assert
        order.Status.ShouldBe(OrderStatus.Completed);
    }

    [Fact]
    public void Complete_ShouldThrow_WhenOrderNotInProcessingStatus()
    {
        // Arrange
        var order = Order.Create();

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => order.Complete());
        exception.Message.ShouldContain("is not in processing status");
    }
}